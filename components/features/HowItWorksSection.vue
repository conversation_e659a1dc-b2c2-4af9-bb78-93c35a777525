<template>
  <section
    class="h-full relative overflow-hidden flex items-center justify-center"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="container mx-auto px-4 relative w-full">
      <!-- Section Header -->
      <div class="text-center mb-16 md:mb-20">
        <h2 class="text-heading-1 md:text-display-2 font-bold text-gray-900 mb-6">
          {{ t('howItWorks.title') }}
        </h2>
        <p class="text-body-large text-gray-600 max-w-2xl mx-auto">
          {{ t('howItWorks.subtitle') }}
        </p>
      </div>

      <!-- Interactive Slider -->
      <div class="max-w-7xl mx-auto">
        <!-- Content Area -->
        <div class="relative h-[600px] md:h-[700px] rounded-3xl shadow-xl border border-gray-200/60 overflow-hidden backdrop-blur-xl bg-transition"
          :class="getStepBackgroundClass(activeStep)">
          <!-- Navigation Arrows -->
          <button
            @click="previousStep"
            :disabled="activeStep === 0"
            class="absolute left-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:shadow-xl hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed z-10"
          >
            <ChevronLeft class="w-5 h-5 text-gray-600" />
          </button>

          <button
            @click="nextStep"
            :disabled="activeStep === steps.length - 1"
            class="absolute right-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:shadow-xl hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed z-10"
          >
            <ChevronRight class="w-5 h-5 text-gray-600" />
          </button>
          <!-- Background Pattern -->
          <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" :style="getStepPatternStyle(activeStep)"></div>
          </div>

          <!-- Step Content -->
          <Transition 
            name="slide" 
            mode="out-in"
          >
            <div :key="activeStep" class="absolute inset-0">
              <!-- Smart Detection View -->
              <div v-if="activeStep === 0" class="h-full flex flex-col">
                <div class="flex-1 flex items-center justify-center p-8 md:p-16">
                  <div class="w-full max-w-4xl mx-auto">
                    <SmartDetectionAnimation />
                  </div>
                </div>
              </div>

              <!-- User Prompt Input View -->
              <div v-else-if="activeStep === 1" class="h-full flex flex-col">
                <div class="flex-1 flex items-center justify-center p-8 md:p-16">
                  <div class="w-full max-w-4xl mx-auto">
                    <UserPromptAnimation />
                  </div>
                </div>
              </div>

              <!-- AI Analysis View -->
              <div v-else-if="activeStep === 2" class="h-full flex flex-col">
                <div class="flex-1 flex items-center justify-center p-8 md:p-16">
                  <div class="w-full max-w-4xl mx-auto">
                    <AIAnalysisAnimation />
                  </div>
                </div>
              </div>

              <!-- Auto-Fill View -->
              <div v-else-if="activeStep === 3" class="h-full flex flex-col">
                <div class="flex-1 flex items-center justify-center p-8 md:p-16">
                  <div class="w-full max-w-4xl mx-auto">
                    <AutoFillAnimation />
                  </div>
                </div>
              </div>
            </div>
          </Transition>

          

          <!-- Progress Indicators -->
          <div class="absolute bottom-8 left-1/2 -translate-x-1/2 flex gap-3 z-10">
            <div
              v-for="(_, index) in steps"
              :key="index"
              @click="setActiveStep(index)"
              class="w-3 h-3 rounded-full transition-all duration-300 cursor-pointer"
              :class="{
                'bg-blue-600 scale-125': activeStep === index,
                'bg-gray-300 hover:bg-gray-400': activeStep !== index
              }"
            ></div>
          </div>
        </div>

        </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'
import SmartDetectionAnimation from '@/components/features/SmartDetectionAnimation.vue'
import AIAnalysisAnimation from '@/components/features/AIAnalysisAnimation.vue'
import AutoFillAnimation from '@/components/features/AutoFillAnimation.vue'
import UserPromptAnimation from '@/components/features/UserPromptAnimation.vue'

const { t } = useI18n()
const activeStep = ref(0)
const isHovering = ref(false)

const steps = ref([
  {
    id: 'detection',
    title: t('howItWorks.steps.detection.title'),
    description: t('howItWorks.steps.detection.description'),
    tags: [t('demo.tags.smartDetection'), t('demo.tags.autoDetection'), t('demo.tags.formFields')]
  },
  {
    id: 'prompt',
    title: 'Describe Your Needs',
    description: 'Simply tell Fillify what you want to fill in the form with a natural language description',
    tags: ['Natural Language', 'User Input', 'Simple Instructions']
  },
  {
    id: 'analysis', 
    title: t('howItWorks.steps.analysis.title'),
    description: t('howItWorks.steps.analysis.description'),
    tags: [t('demo.tags.aiAnalysis'), t('demo.tags.multiModel'), t('demo.tags.smartUnderstanding')]
  },
  {
    id: 'autoFill',
    title: t('howItWorks.steps.autoFill.title'),
    description: t('howItWorks.steps.autoFill.description'),
    tags: [t('demo.tags.autoFill'), t('demo.tags.quickComplete'), t('demo.tags.improveEfficiency')]
  }
])

const setActiveStep = (index: number) => {
  if (index >= 0 && index < steps.value.length) {
    activeStep.value = index
  }
}

const nextStep = () => {
  if (activeStep.value < steps.value.length - 1) {
    activeStep.value++
  }
}

const previousStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

// 鼠标悬停控制
const handleMouseEnter = () => {
  isHovering.value = true
  stopAutoPlay()
}

const handleMouseLeave = () => {
  isHovering.value = false
  startAutoPlay()
}

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowLeft':
      previousStep()
      break
    case 'ArrowRight':
      nextStep()
      break
    case '1':
    case '2':
    case '3':
    case '4':
      const stepIndex = parseInt(event.key) - 1
      setActiveStep(stepIndex)
      break
  }
}

// 自动轮播
let autoPlayTimer: NodeJS.Timeout | null = null

const startAutoPlay = () => {
  if (autoPlayTimer) return // 防止重复启动
  
  autoPlayTimer = setInterval(() => {
    if (!isHovering.value) {
      if (activeStep.value === steps.value.length - 1) {
        activeStep.value = 0
      } else {
        nextStep()
      }
    }
  }, 8000) // 8秒切换一次
}

const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

// 马卡龙色系背景类
const getStepBackgroundClass = (index: number) => {
  const classes = [
    'bg-blue-50/70',   // 智能检测 - 蓝色系
    'bg-yellow-50/70', // 用户输入 - 黄色系
    'bg-purple-50/70', // AI分析 - 紫色系
    'bg-green-50/70'   // 自动填充 - 绿色系
  ]
  return classes[index] || 'bg-white/70'
}

// 背景图案样式
const getStepPatternStyle = (index: number) => {
  const styles = [
    'background-image: radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.15) 1px, transparent 0); background-size: 20px 20px;',  // 蓝色系
    'background-image: radial-gradient(circle at 2px 2px, rgba(251, 191, 36, 0.15) 1px, transparent 0); background-size: 20px 20px;',   // 黄色系
    'background-image: radial-gradient(circle at 2px 2px, rgba(168, 85, 247, 0.15) 1px, transparent 0); background-size: 20px 20px;', // 紫色系
    'background-image: radial-gradient(circle at 2px 2px, rgba(34, 197, 94, 0.15) 1px, transparent 0); background-size: 20px 20px;'   // 绿色系
  ]
  return styles[index] || 'background-image: radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.15) 1px, transparent 0); background-size: 20px 20px;'
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  startAutoPlay()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  stopAutoPlay()
})
</script>

<style scoped>
/* 主容器样式 */
section {
  position: relative;
}

/* 切换动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(50px) scale(0.95);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-50px) scale(0.95);
}

/* 背景过渡动画 */
.bg-transition {
  transition: background-color 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 导航按钮样式 */
button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

button:disabled:hover {
  transform: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 进度指示器动画 */
.progress-dot {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-dot:hover {
  transform: scale(1.2);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .slide-enter-from {
    transform: translateY(30px) scale(0.95);
  }
  
  .slide-leave-to {
    transform: translateY(-30px) scale(0.95);
  }
}

/* 焦点样式 */
button:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* 滑动容器优化 */
@media (prefers-reduced-motion: reduce) {
  .slide-enter-active,
  .slide-leave-active,
  .fade-enter-active,
  .fade-leave-active {
    transition: none;
  }
  
  .slide-enter-from,
  .slide-leave-to {
    transform: none;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>